# 🎨 Esquema de Colores Oficial - Municipio de Chía

## 📋 Resumen Ejecutivo

Este documento describe la implementación del esquema de colores oficial del Municipio de Chía en el portal web, garantizando cumplimiento con estándares de accesibilidad WCAG 2.1 AA y consistencia visual corporativa.

## 🏛️ Colores Institucionales

### Color Primario - Verde Chía
- **Oficial**: `#059669` (chia-green-600) - Color corporativo oficial
- **Implementación**: `#15803d` (chia-green-700) - Color primario para texto y elementos interactivos
- **Razón del cambio**: El color oficial no cumple WCAG 2.1 AA (ratio 3.77:1), por lo que se usa una versión más oscura (ratio 5.02:1)

### Color Secundario - Azul Chía
- **Color**: `#1E40AF` (chia-blue-800) - Azul institucional
- **Uso**: Elementos secundarios, enlaces alternativos, decoración

## 🎯 Implementación Técnica

### Variables CSS (globals.css)
```css
/* Modo Claro */
--primary: 150 69% 24%; /* Chia Green 700 - Cumple WCAG 2.1 AA */
--primary-foreground: 0 0% 100%; /* Blanco para contraste */
--ring: 150 69% 24%; /* Focus ring */

/* Modo Oscuro */
--primary: 150 84% 50%; /* Verde más claro para modo oscuro */
--ring: 150 84% 50%; /* Focus ring modo oscuro */
```

### Configuración Tailwind (tailwind.config.js)
```javascript
primary: {
  600: '#059669', // Verde oficial de Chía
  700: '#15803d', // Color primario - Cumple WCAG 2.1 AA
  DEFAULT: '#15803d', // Establece 700 como color por defecto
}
```

## ✅ Verificación de Accesibilidad

### Ratios de Contraste WCAG 2.1 AA
| Combinación | Ratio | Estado | Uso |
|-------------|-------|--------|-----|
| Verde 700 sobre blanco | 5.02:1 | ✅ PASA | Texto, botones |
| Blanco sobre verde 700 | 5.02:1 | ✅ PASA | Botones primarios |
| Verde 700 sobre gris claro | 4.80:1 | ✅ PASA | Elementos sobre fondos |
| Verde 600 sobre blanco | 3.77:1 | ❌ FALLA | Solo decorativo |

### Herramienta de Verificación
```bash
# Ejecutar verificación de contraste
node scripts/verify-color-contrast.js
```

## 🎨 Guía de Uso

### Colores Primarios
- **Texto y enlaces**: Usar `text-primary` (verde 700)
- **Botones principales**: Usar `bg-primary` (verde 700)
- **Bordes activos**: Usar `border-primary` (verde 700)
- **Focus rings**: Automático con `ring-primary` (verde 700)

### Colores Decorativos
- **Fondos sutiles**: Usar `bg-primary/10` (verde 700 con 10% opacidad)
- **Elementos gráficos**: Usar `chia-green-600` (color oficial)
- **Iconos decorativos**: Usar `text-chia-green-600`

### Colores Secundarios
- **Enlaces alternativos**: Usar `text-chia-blue-800`
- **Elementos informativos**: Usar `bg-chia-blue-50`
- **Bordes secundarios**: Usar `border-chia-blue-200`

## 🔧 Componentes Actualizados

### Button Component
```tsx
// ANTES
className="bg-chia-blue-600 text-white"

// DESPUÉS
className="bg-primary text-primary-foreground"
```

### Badge Component
```tsx
// ANTES
className="bg-blue-100 text-blue-800"

// DESPUÉS
className="bg-primary/10 text-primary"
```

### Navigation Components
```tsx
// ANTES
className="text-chia-blue-600 hover:text-chia-blue-700"

// DESPUÉS
className="text-primary hover:text-primary/80"
```

## 📊 Impacto en Pruebas

### Pruebas Actualizadas
- `tests/unit/components/ui/Button.test.tsx`
  - Cambio de `bg-chia-blue-600` a `bg-primary`
  - Cambio de `text-chia-blue-600` a `text-primary`
  - Actualización de tamaños de botones (h-10 → h-11, etc.)

### Comando de Pruebas
```bash
# Ejecutar pruebas específicas de componentes UI
npm test -- tests/unit/components/ui/
```

## 🚀 Beneficios Implementados

### Accesibilidad
- ✅ Cumplimiento WCAG 2.1 AA (ratio mínimo 4.5:1)
- ✅ Mejor legibilidad para usuarios con discapacidades visuales
- ✅ Compatibilidad con lectores de pantalla

### Consistencia Visual
- ✅ Uso sistemático del color corporativo oficial
- ✅ Jerarquía visual clara con colores primarios y secundarios
- ✅ Coherencia en toda la aplicación

### Mantenibilidad
- ✅ Variables CSS centralizadas
- ✅ Clases semánticas (`primary` vs colores específicos)
- ✅ Fácil actualización futura de colores

## 📝 Próximos Pasos

1. **Validación Visual**: Revisar todas las páginas para consistencia
2. **Pruebas E2E**: Ejecutar pruebas end-to-end completas
3. **Documentación Usuario**: Actualizar guías de usuario si es necesario
4. **Feedback Stakeholders**: Obtener aprobación de cambios visuales

## 🔗 Referencias

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Color Contrast Analyzer](https://www.tpgi.com/color-contrast-checker/)
- [Tailwind CSS Color Palette](https://tailwindcss.com/docs/customizing-colors)
- [Shadcn/ui Theming](https://ui.shadcn.com/docs/theming)

---

**Fecha de Implementación**: 2025-01-04  
**Versión**: 1.0  
**Estado**: ✅ Implementado y Verificado
