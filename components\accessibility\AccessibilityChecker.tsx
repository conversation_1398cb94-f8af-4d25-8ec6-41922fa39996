'use client'

import React, { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CheckCircle, AlertTriangle, XCircle, Eye, Palette, Type, MousePointer } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AccessibilityIssue {
  type: 'error' | 'warning' | 'success'
  category: 'color' | 'text' | 'navigation' | 'interaction'
  message: string
  element?: string
  recommendation?: string
}

interface ColorContrastResult {
  foreground: string
  background: string
  ratio: number
  passes: {
    aa: boolean
    aaa: boolean
  }
  level: 'AA' | 'AAA' | 'FAIL'
}

export function AccessibilityChecker({ className }: { className?: string }) {
  const [issues, setIssues] = useState<AccessibilityIssue[]>([])
  const [colorTests, setColorTests] = useState<ColorContrastResult[]>([])
  const [isChecking, setIsChecking] = useState(false)

  // Colores del sistema Chía para verificar
  const chiaColors = {
    'chia-blue-900': '#1e3a8a',
    'chia-blue-800': '#1e40af', 
    'chia-blue-600': '#2563eb',
    'chia-green-600': '#059669',
    'chia-green-700': '#15803d',
    'white': '#ffffff',
    'gray-50': '#f9fafb',
    'gray-100': '#f3f4f6',
    'gray-600': '#4b5563',
    'gray-900': '#111827'
  }

  // Función para calcular contraste de color
  const calculateContrast = (color1: string, color2: string): number => {
    const getLuminance = (hex: string): number => {
      const rgb = parseInt(hex.slice(1), 16)
      const r = (rgb >> 16) & 0xff
      const g = (rgb >> 8) & 0xff
      const b = (rgb >> 0) & 0xff

      const [rs, gs, bs] = [r, g, b].map(c => {
        c = c / 255
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
      })

      return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
    }

    const lum1 = getLuminance(color1)
    const lum2 = getLuminance(color2)
    const brightest = Math.max(lum1, lum2)
    const darkest = Math.min(lum1, lum2)

    return (brightest + 0.05) / (darkest + 0.05)
  }

  // Verificar contrastes de color
  const checkColorContrasts = () => {
    const combinations = [
      { fg: chiaColors['chia-blue-900'], bg: chiaColors.white, name: 'Texto principal sobre blanco' },
      { fg: chiaColors['chia-blue-800'], bg: chiaColors.white, name: 'Enlaces sobre blanco' },
      { fg: chiaColors.white, bg: chiaColors['chia-blue-600'], name: 'Texto blanco sobre azul' },
      { fg: chiaColors.white, bg: chiaColors['chia-green-600'], name: 'Texto blanco sobre verde' },
      { fg: chiaColors['chia-green-700'], bg: chiaColors.white, name: 'Verde oscuro sobre blanco' },
      { fg: chiaColors['gray-600'], bg: chiaColors.white, name: 'Texto secundario sobre blanco' },
      { fg: chiaColors['gray-900'], bg: chiaColors['gray-50'], name: 'Texto sobre fondo gris claro' }
    ]

    const results: ColorContrastResult[] = combinations.map(combo => {
      const ratio = calculateContrast(combo.fg, combo.bg)
      return {
        foreground: combo.fg,
        background: combo.bg,
        ratio,
        passes: {
          aa: ratio >= 4.5,
          aaa: ratio >= 7
        },
        level: ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : 'FAIL'
      }
    })

    setColorTests(results)
    return results
  }

  // Verificar accesibilidad general
  const runAccessibilityCheck = async () => {
    setIsChecking(true)
    const newIssues: AccessibilityIssue[] = []

    // 1. Verificar contrastes de color
    const colorResults = checkColorContrasts()
    colorResults.forEach((result, index) => {
      if (!result.passes.aa) {
        newIssues.push({
          type: 'error',
          category: 'color',
          message: `Contraste insuficiente: ${result.ratio.toFixed(2)}:1`,
          recommendation: 'Usar colores con mayor contraste para cumplir WCAG 2.1 AA (mínimo 4.5:1)'
        })
      } else if (!result.passes.aaa) {
        newIssues.push({
          type: 'warning',
          category: 'color',
          message: `Contraste AA pero no AAA: ${result.ratio.toFixed(2)}:1`,
          recommendation: 'Considerar mejorar contraste para nivel AAA (7:1)'
        })
      } else {
        newIssues.push({
          type: 'success',
          category: 'color',
          message: `Excelente contraste: ${result.ratio.toFixed(2)}:1`,
          recommendation: 'Cumple con WCAG 2.1 AAA'
        })
      }
    })

    // 2. Verificar elementos de navegación
    const navElements = document.querySelectorAll('nav, [role="navigation"]')
    if (navElements.length > 0) {
      newIssues.push({
        type: 'success',
        category: 'navigation',
        message: `${navElements.length} elemento(s) de navegación encontrados`,
        recommendation: 'Navegación correctamente marcada'
      })
    }

    // 3. Verificar enlaces y botones
    const interactiveElements = document.querySelectorAll('button, a, [role="button"]')
    const elementsWithoutText = Array.from(interactiveElements).filter(el => 
      !el.textContent?.trim() && !el.getAttribute('aria-label')
    )
    
    if (elementsWithoutText.length > 0) {
      newIssues.push({
        type: 'warning',
        category: 'interaction',
        message: `${elementsWithoutText.length} elemento(s) interactivos sin texto accesible`,
        recommendation: 'Agregar aria-label o texto visible a todos los elementos interactivos'
      })
    }

    // 4. Verificar headings
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
    if (headings.length > 0) {
      newIssues.push({
        type: 'success',
        category: 'text',
        message: `${headings.length} encabezados encontrados`,
        recommendation: 'Estructura de encabezados presente'
      })
    }

    setIssues(newIssues)
    setIsChecking(false)
  }

  useEffect(() => {
    // Ejecutar verificación automática al montar
    runAccessibilityCheck()
  }, [])

  const getIssueIcon = (type: AccessibilityIssue['type']) => {
    switch (type) {
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'success': return <CheckCircle className="h-4 w-4 text-green-500" />
    }
  }

  const getCategoryIcon = (category: AccessibilityIssue['category']) => {
    switch (category) {
      case 'color': return <Palette className="h-4 w-4" />
      case 'text': return <Type className="h-4 w-4" />
      case 'navigation': return <Eye className="h-4 w-4" />
      case 'interaction': return <MousePointer className="h-4 w-4" />
    }
  }

  const errorCount = issues.filter(i => i.type === 'error').length
  const warningCount = issues.filter(i => i.type === 'warning').length
  const successCount = issues.filter(i => i.type === 'success').length

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Eye className="h-5 w-5 mr-2 text-chia-blue-600" />
              Verificación de Accesibilidad WCAG 2.1 AA
            </CardTitle>
            <CardDescription>
              Validación automática de estándares de accesibilidad para el portal municipal
            </CardDescription>
          </div>
          <Button 
            onClick={runAccessibilityCheck} 
            disabled={isChecking}
            variant="outline"
          >
            {isChecking ? 'Verificando...' : 'Verificar'}
          </Button>
        </div>
        
        <div className="flex space-x-2 mt-4">
          <Badge variant={errorCount > 0 ? "destructive" : "secondary"}>
            {errorCount} Errores
          </Badge>
          <Badge variant={warningCount > 0 ? "default" : "secondary"}>
            {warningCount} Advertencias  
          </Badge>
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            {successCount} Exitosos
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Resultados de contraste de color */}
        <div>
          <h4 className="font-semibold mb-2 flex items-center">
            <Palette className="h-4 w-4 mr-2" />
            Contrastes de Color
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {colorTests.map((test, index) => (
              <div key={index} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-4 h-4 border"
                    style={{ backgroundColor: test.foreground }}
                  />
                  <div 
                    className="w-4 h-4 border"
                    style={{ backgroundColor: test.background }}
                  />
                  <span className="text-sm">{test.ratio.toFixed(2)}:1</span>
                </div>
                <Badge 
                  variant={test.level === 'FAIL' ? 'destructive' : test.level === 'AA' ? 'default' : 'secondary'}
                  className={test.level === 'AAA' ? 'bg-green-100 text-green-800' : ''}
                >
                  {test.level}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* Lista de issues */}
        <div className="space-y-2">
          {issues.map((issue, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
              {getIssueIcon(issue.type)}
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  {getCategoryIcon(issue.category)}
                  <span className="font-medium text-sm">{issue.message}</span>
                </div>
                {issue.recommendation && (
                  <p className="text-xs text-gray-600">{issue.recommendation}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
